# LangExtract 使用说明

## 快速开始

### 1. 激活环境
```bash
.\langextract_env\Scripts\activate
```

### 2. 设置API密钥
编辑 `.env` 文件，设置你的 Gemini API 密钥：
```
LANGEXTRACT_API_KEY=你的API密钥
```

### 3. 运行演示
```bash
python demo.py
```

或者双击 `启动.bat`

## 功能特性

- ✅ 支持中文和日语文本提取
- ✅ 修复编码问题，正确显示中日文
- ✅ 自动生成可视化HTML文件
- ✅ 保存结果为JSONL格式

## 文件说明

- `demo.py` - 统一演示脚本
- `启动.bat` - Windows启动脚本
- `.env` - API密钥配置文件
- `extraction_results_*.jsonl` - 提取结果文件
- `visualization_*.html` - 可视化文件

## 获取API密钥

1. 访问 [Google AI Studio](https://aistudio.google.com/)
2. 创建API密钥
3. 复制密钥到 `.env` 文件

## 支持的语言

- 中文：提取人物、情感、关系
- 日语：提取人物、感情、関係性

## 故障排除

如果遇到编码问题，确保：
1. 使用UTF-8编码保存文件
2. Windows控制台设置为UTF-8
3. 检查生成的JSONL文件是否正确显示中日文
