import os
if "LOCAL_RANK" not in os.environ:
    os.environ["LOCAL_RANK"] = "0"

import torch
from transformers import AutoModelForCausalLM, AutoProcessor, AutoTokenizer
from qwen_vl_utils import process_vision_info
from dots_ocr.utils import dict_promptmode_to_prompt

def inference(image_path, prompt, model, processor):
    # image_path = "demo/demo_image1.jpg"
    messages = [
        {
            "role": "user",
            "content": [
                {
                    "type": "image",
                    "image": image_path
                },
                {"type": "text", "text": prompt}
            ]
        }
    ]


    # Preparation for inference
    text = processor.apply_chat_template(
        messages, 
        tokenize=False, 
        add_generation_prompt=True
    )
    image_inputs, video_inputs = process_vision_info(messages)
    inputs = processor(
        text=[text],
        images=image_inputs,
        videos=video_inputs,
        padding=True,
        return_tensors="pt",
    )

    # 使用CPU设备
    device = "cpu"
    inputs = inputs.to(device)

    # 强制转换所有输入为float32和CPU
    def force_convert_inputs(inputs_dict):
        converted = {}
        for k, v in inputs_dict.items():
            if isinstance(v, torch.Tensor):
                if v.dtype.is_floating_point and v.dtype != torch.float32:
                    print(f"转换 {k}: {v.dtype} -> float32")
                    converted[k] = v.to(dtype=torch.float32, device="cpu")
                else:
                    converted[k] = v.to(device="cpu")
            else:
                converted[k] = v
        return converted

    inputs = force_convert_inputs(inputs)

    # Inference: Generation of the output
    generated_ids = model.generate(**inputs, max_new_tokens=2000)
    generated_ids_trimmed = [
        out_ids[len(in_ids) :] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
    ]
    output_text = processor.batch_decode(
        generated_ids_trimmed, skip_special_tokens=True, clean_up_tokenization_spaces=False
    )
    print(output_text)



if __name__ == "__main__":
    # We recommend enabling flash_attention_2 for better acceleration and memory saving, especially in multi-image and video scenarios.
    model_path = "./weights/DotsOCR"
    model = AutoModelForCausalLM.from_pretrained(
        model_path,
        attn_implementation="eager",  # 使用eager attention而不是flash attention
        torch_dtype=torch.float32,   # CPU使用float32
        device_map="cpu",            # 强制使用CPU
        trust_remote_code=True
    )
    # 确保模型在CPU上并使用float32
    model = model.to("cpu").to(torch.float32)

    # 递归转换所有参数和缓冲区
    def force_float32_conversion(module):
        for child in module.children():
            force_float32_conversion(child)
        for param in module.parameters(recurse=False):
            if param.dtype != torch.float32:
                param.data = param.data.to(torch.float32)
        for name, buffer in module.named_buffers(recurse=False):
            if buffer.dtype != torch.float32:
                buffer.data = buffer.data.to(torch.float32)

    print("强制转换模型参数为float32...")
    force_float32_conversion(model)
    processor = AutoProcessor.from_pretrained(model_path,  trust_remote_code=True)

    image_path = "demo/demo_image1.jpg"
    for prompt_mode, prompt in dict_promptmode_to_prompt.items():
        print(f"prompt: {prompt}")
        inference(image_path, prompt, model, processor)
    