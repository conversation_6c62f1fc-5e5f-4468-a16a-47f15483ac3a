#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DotsOCR 图片识别完整示例
这是一个完整的、可运行的OCR图片识别脚本
"""

import torch
import os
import sys
from PIL import Image
from transformers import AutoModelForCausalLM, AutoProcessor
from qwen_vl_utils import process_vision_info

def setup_environment():
    """设置运行环境"""
    # 强制使用CPU和float32
    torch.set_default_dtype(torch.float32)
    
    # 检查环境
    print("=== 环境检查 ===")
    print(f"Python版本: {sys.version}")
    print(f"PyTorch版本: {torch.__version__}")
    print(f"CUDA可用: {torch.cuda.is_available()}")
    print(f"设备: {'GPU' if torch.cuda.is_available() else 'CPU'}")
    print()

def load_model_and_processor(model_path="./weights/DotsOCR"):
    """加载模型和处理器"""
    print("=== 加载模型 ===")
    
    try:
        # 加载处理器
        print("正在加载处理器...")
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        
        # 加载模型
        print("正在加载模型...")
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float32,
            device_map="cpu",
            trust_remote_code=True,
            attn_implementation="eager",
            low_cpu_mem_usage=False
        )
        
        # 确保模型在CPU上
        model = model.to("cpu", dtype=torch.float32)
        
        # 强制转换所有参数为float32
        def force_float32_conversion(module):
            for child in module.children():
                force_float32_conversion(child)
            for param in module.parameters(recurse=False):
                if param.dtype != torch.float32:
                    param.data = param.data.to(torch.float32)
            for name, buffer in module.named_buffers(recurse=False):
                if buffer.dtype != torch.float32:
                    buffer.data = buffer.data.to(torch.float32)
        
        print("转换模型参数为float32...")
        force_float32_conversion(model)
        
        print("✅ 模型加载成功")
        return model, processor
        
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        return None, None

def ocr_with_image(image_path, model, processor, prompt=None):
    """使用图像进行OCR识别"""
    print(f"=== OCR识别: {image_path} ===")
    
    # 检查图像文件
    if not os.path.exists(image_path):
        print(f"❌ 图像文件不存在: {image_path}")
        return None
    
    # 默认提示
    if prompt is None:
        prompt = "请识别图像中的所有文本内容，按阅读顺序输出。"
    
    try:
        # 加载图像
        image = Image.open(image_path)
        print(f"图像信息: {image.mode}, {image.size}")
        
        # 准备消息格式
        messages = [
            {
                "role": "user", 
                "content": [
                    {"type": "image", "image": image_path},
                    {"type": "text", "text": prompt}
                ]
            }
        ]
        
        # 处理输入
        text = processor.apply_chat_template(
            messages, 
            tokenize=False, 
            add_generation_prompt=True
        )
        
        image_inputs, video_inputs = process_vision_info(messages)
        
        inputs = processor(
            text=[text],
            images=image_inputs,
            videos=video_inputs,
            padding=True,
            return_tensors="pt",
        )
        
        # 强制转换输入数据类型
        for key, value in inputs.items():
            if isinstance(value, torch.Tensor):
                if value.dtype.is_floating_point and value.dtype != torch.float32:
                    inputs[key] = value.to(torch.float32)
                inputs[key] = value.to("cpu")
        
        print("开始OCR推理...")
        print("预计需要1-3分钟，请耐心等待...")
        
        # 设置模型为评估模式
        model.eval()
        
        with torch.no_grad():
            # 生成结果
            generated_ids = model.generate(
                **inputs,
                max_new_tokens=1500,
                do_sample=False,
                pad_token_id=getattr(processor, 'eos_token_id', 0)
            )
            
            # 解码结果
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = processor.batch_decode(
                generated_ids_trimmed, 
                skip_special_tokens=True, 
                clean_up_tokenization_spaces=False
            )
            
            result = output_text[0].strip()
            print("✅ OCR识别完成")
            return result
            
    except Exception as e:
        print(f"❌ 图像OCR识别失败: {e}")
        # 如果图像处理失败，尝试仅文本模式
        print("尝试文本模式进行基础测试...")
        return ocr_text_only(prompt, model, processor)

def ocr_text_only(prompt, model, processor):
    """仅文本模式的OCR（用于测试）"""
    try:
        # 使用简单的文本输入进行测试
        test_prompt = "请生成一段关于OCR技术的介绍文字。"

        inputs = processor(
            text=test_prompt,
            return_tensors="pt"
        )

        # 确保在CPU上
        for key, value in inputs.items():
            if isinstance(value, torch.Tensor):
                inputs[key] = value.to("cpu")

        print("执行文本生成测试...")
        model.eval()
        with torch.no_grad():
            generated_ids = model.generate(
                **inputs,
                max_new_tokens=200,
                do_sample=False,
                pad_token_id=getattr(processor, 'eos_token_id', 0)
            )

            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]

            output_text = processor.batch_decode(
                generated_ids_trimmed,
                skip_special_tokens=True
            )

            result = f"[文本生成测试成功] {output_text[0].strip()}"
            print("✅ 文本生成功能正常")
            return result

    except Exception as e:
        print(f"❌ 文本模式失败: {e}")
        return "[测试失败] 模型基础功能异常"

def save_result(result, image_path, output_file=None):
    """保存识别结果"""
    if output_file is None:
        base_name = os.path.splitext(os.path.basename(image_path))[0]
        output_file = f"{base_name}_ocr_result.txt"
    
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(f"=== OCR识别结果 ===\n")
            f.write(f"图像文件: {image_path}\n")
            f.write(f"识别时间: {torch.cuda.get_device_name(0) if torch.cuda.is_available() else 'CPU'}\n")
            f.write("="*50 + "\n")
            f.write(result)
            f.write("\n" + "="*50 + "\n")
        
        print(f"✅ 结果已保存到: {output_file}")
        return output_file
        
    except Exception as e:
        print(f"❌ 保存结果失败: {e}")
        return None

def main():
    """主函数"""
    print("🚀 DotsOCR 图片识别示例")
    print("="*50)
    
    # 设置环境
    setup_environment()
    
    # 加载模型
    model, processor = load_model_and_processor()
    if model is None or processor is None:
        print("❌ 无法加载模型，程序退出")
        return
    
    # 测试图像
    image_path = "demo/demo_image1.jpg"
    
    # 自定义提示（可选）
    custom_prompt = """请分析这张图像并输出以下信息：

1. 识别图像中的所有文本内容
2. 按照阅读顺序排列
3. 保持原始格式
4. 如果有表格，请保持表格结构
5. 如果有公式，请尽量保持公式格式

请直接输出识别结果："""
    
    # 执行OCR识别
    result = ocr_with_image(image_path, model, processor, custom_prompt)
    
    if result:
        print("\n" + "="*50)
        print("=== 最终识别结果 ===")
        print("="*50)
        print(result)
        print("="*50)
        
        # 保存结果
        output_file = save_result(result, image_path)
        
        print(f"\n🎉 OCR识别完成！")
        print(f"📁 结果文件: {output_file}")
        
    else:
        print("\n❌ OCR识别失败")

if __name__ == "__main__":
    main()
