#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门识别demo_image1.jpg的OCR脚本
"""

import torch
from transformers import AutoModelForCausalLM, AutoProcessor
from qwen_vl_utils import process_vision_info

def recognize_image():
    """识别demo_image1.jpg中的文本内容"""
    print("正在识别图片内容...")
    
    # 设置环境
    torch.set_default_dtype(torch.float32)
    model_path = "./weights/DotsOCR"
    image_path = "demo/demo_image1.jpg"
    
    try:
        # 加载模型和处理器
        processor = AutoProcessor.from_pretrained(model_path, trust_remote_code=True)
        model = AutoModelForCausalLM.from_pretrained(
            model_path,
            torch_dtype=torch.float32,
            device_map="cpu",
            trust_remote_code=True,
            attn_implementation="eager"
        )
        
        # 转换为float32
        model = model.to("cpu", dtype=torch.float32)
        
        def force_float32(module):
            for child in module.children():
                force_float32(child)
            for param in module.parameters(recurse=False):
                if param.dtype != torch.float32:
                    param.data = param.data.to(torch.float32)
            for name, buffer in module.named_buffers(recurse=False):
                if buffer.dtype != torch.float32:
                    buffer.data = buffer.data.to(torch.float32)
        
        force_float32(model)
        
        # 准备OCR提示
        prompt = "请识别图像中的所有文本内容。"
        
        # 准备消息
        messages = [
            {
                "role": "user",
                "content": [
                    {"type": "image", "image": image_path},
                    {"type": "text", "text": prompt}
                ]
            }
        ]
        
        # 处理输入 - 使用修复后的方法
        try:
            text = processor.apply_chat_template(messages, tokenize=False, add_generation_prompt=True)
            image_inputs, video_inputs = process_vision_info(messages)

            inputs = processor(
                text=[text],
                images=image_inputs,
                videos=video_inputs,
                padding=True,
                return_tensors="pt",
            )
        except Exception as e:
            print(f"消息处理失败: {e}")
            print("使用备用方法...")
            # 备用方法：直接使用原始demo_hf.py的方法
            from demo.demo_hf import inference
            result = inference(image_path, prompt, model, processor)
            return result
        
        # 转换数据类型
        for key, value in inputs.items():
            if isinstance(value, torch.Tensor):
                if value.dtype.is_floating_point and value.dtype != torch.float32:
                    inputs[key] = value.to(torch.float32)
                inputs[key] = value.to("cpu")
        
        print("开始识别...")
        
        # 生成结果
        model.eval()
        with torch.no_grad():
            generated_ids = model.generate(
                **inputs,
                max_new_tokens=2000,
                do_sample=False,
                pad_token_id=getattr(processor, 'eos_token_id', 0)
            )
            
            generated_ids_trimmed = [
                out_ids[len(in_ids):] for in_ids, out_ids in zip(inputs.input_ids, generated_ids)
            ]
            
            output_text = processor.batch_decode(
                generated_ids_trimmed, 
                skip_special_tokens=True, 
                clean_up_tokenization_spaces=False
            )
            
            result = output_text[0].strip()
            
            print("\n" + "="*60)
            print("图片识别结果:")
            print("="*60)
            print(result)
            print("="*60)
            
            # 保存结果
            with open("image_recognition_result.txt", 'w', encoding='utf-8') as f:
                f.write("demo_image1.jpg 识别结果:\n")
                f.write("="*60 + "\n")
                f.write(result)
                f.write("\n" + "="*60)
            
            print("\n结果已保存到: image_recognition_result.txt")
            return result
            
    except Exception as e:
        print(f"识别失败: {e}")
        return None

if __name__ == "__main__":
    recognize_image()
