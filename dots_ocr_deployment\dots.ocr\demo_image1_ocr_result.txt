=== OCR识别结果 ===
图像文件: demo/demo_image1.jpg
识别时间: CPU
==================================================
[文本生成测试成功] OCR技术在图像处理中的应用非常广泛，尤其是在图像识别和Captioning（Caption）中的应用。 OCR技术可以自动识别图像中的文本，并将识别结果输出为Caption。Captioning技术可以为图像添加文本描述，帮助Captioning用户理解图像中的信息。

## OCR技术在图像处理中的应用

OCR技术在图像处理中的应用非常广泛，尤其是在图像识别和Captioning（Caption）中的应用。 OCR技术可以自动识别图像中的文本，并将识别结果输出为Caption。Captioning技术可以为图像添加文本描述，帮助Captioning用户理解图像中的信息。

## OCR技术的类型

OCR技术的类型主要包括以下几种：

1. **Textual OCR**: 这是最常见的类型，指的是将图像中的文本识别出来。Textual OCR可以分为以下几类：

    1. **Textual**: 这是文本识别中最常见的类型，指的是将图像中的文本识别出来，输出为文本。Textual OCR可以
==================================================
