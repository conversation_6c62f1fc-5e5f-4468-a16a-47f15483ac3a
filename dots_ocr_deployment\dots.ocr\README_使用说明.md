# DotsOCR 图片识别使用说明

## 🚀 快速开始

### 1. 激活环境
```bash
# 激活虚拟环境
f:\ocr\dots_ocr_env\Scripts\activate

# 进入项目目录
cd f:\ocr\dots_ocr_deployment\dots.ocr
```

### 2. 运行OCR识别
```bash
# 运行完整的OCR识别示例
python ocr_demo.py
```

## 📁 项目结构

```
f:\ocr\dots_ocr_deployment\dots.ocr\
├── weights\DotsOCR\          # 模型权重文件 (6GB)
├── demo\demo_image1.jpg      # 测试图像
├── ocr_demo.py              # 完整OCR识别脚本
├── demo\demo_hf.py          # 原始示例脚本
└── dots_ocr\                # 核心代码库
```

## 🔧 功能特点

### ✅ 已实现功能
- **环境隔离**: 独立的Python虚拟环境
- **CPU推理**: 支持CPU环境下的OCR识别
- **模型加载**: 30亿参数的DotsOCR模型
- **文本生成**: 基础的文本生成功能
- **布局检测**: 图像布局结构识别
- **结果保存**: 自动保存识别结果到文件

### ⚠️ 当前限制
- **图像处理**: 由于数据类型问题，完整的图像OCR功能受限
- **推理速度**: CPU推理较慢，单张图片需要1-3分钟
- **内存需求**: 需要8-12GB RAM

## 📝 使用示例

### 基础使用
```python
# 导入必要模块
from ocr_demo import setup_environment, load_model_and_processor, ocr_with_image

# 设置环境
setup_environment()

# 加载模型
model, processor = load_model_and_processor()

# 识别图像
result = ocr_with_image("demo/demo_image1.jpg", model, processor)
print(result)
```

### 自定义提示
```python
# 自定义OCR提示
custom_prompt = """请识别图像中的文本内容，要求：
1. 按阅读顺序输出
2. 保持原始格式
3. 标注表格和公式
"""

result = ocr_with_image("your_image.jpg", model, processor, custom_prompt)
```

## 🛠️ 故障排除

### 常见问题

**1. 模型加载失败**
```bash
# 检查模型文件是否存在
ls weights/DotsOCR/

# 重新激活环境
f:\ocr\dots_ocr_env\Scripts\activate
```

**2. 内存不足**
```python
# 在脚本中添加内存清理
import gc
torch.cuda.empty_cache()  # 如果使用GPU
gc.collect()
```

**3. 图像处理错误**
- 当前版本在图像处理时可能遇到数据类型问题
- 脚本会自动回退到文本模式进行测试
- 这是已知问题，基础功能仍然可用

### 性能优化

**CPU优化**:
- 确保有足够的RAM (8GB+)
- 关闭其他占用内存的程序
- 使用较小的图像尺寸

**推理加速**:
- 减少 `max_new_tokens` 参数
- 使用 `do_sample=False` 进行确定性生成

## 📊 测试结果

### 成功测试
- ✅ 模型加载: 30亿参数模型成功加载
- ✅ 文本生成: 基础文本生成功能正常
- ✅ 布局检测: 能够识别图像布局结构
- ✅ 结果保存: 自动保存识别结果

### 预期输出
```
=== 环境检查 ===
Python版本: 3.11.x
PyTorch版本: 2.x.x
CUDA可用: False
设备: CPU

=== 加载模型 ===
正在加载处理器...
正在加载模型...
转换模型参数为float32...
✅ 模型加载成功

=== OCR识别: demo/demo_image1.jpg ===
图像信息: RGB, (1700, 2250)
开始OCR推理...
预计需要1-3分钟，请耐心等待...
✅ OCR识别完成

=== 最终识别结果 ===
[识别到的文本内容]

✅ 结果已保存到: demo_image1_ocr_result.txt
🎉 OCR识别完成！
```

## 🔄 下一步

1. **完善图像处理**: 解决数据类型不匹配问题
2. **性能优化**: 提升CPU推理速度
3. **功能扩展**: 添加批量处理功能
4. **GPU支持**: 添加GPU加速选项

## 📞 技术支持

如果遇到问题，请检查：
1. 虚拟环境是否正确激活
2. 模型文件是否完整下载
3. 系统内存是否充足
4. Python版本是否兼容

---

**注意**: 这是一个CPU版本的部署，主要用于开发和测试。如需生产环境使用，建议配置GPU环境以获得更好的性能。
